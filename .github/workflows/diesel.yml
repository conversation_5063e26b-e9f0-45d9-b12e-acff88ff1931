name: diesel-tests

on:
  pull_request:
    paths:
      - '.github/workflows/diesel.yml'
      - 'examples/diesel_*/**'
      - 'sea-query-diesel/**'
      - 'src/**'
  push:
    branches:
      - master
      - 0.*.x
      - pr/**/ci
      - ci-*
    paths:
      - '.github/workflows/diesel.yml'
      - 'examples/diesel_*/**'
      - 'sea-query-diesel/**'
      - 'src/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref || github.run_id }}
  cancel-in-progress: true

env:
  CARGO_TERM_COLOR: always
  SCCACHE_GHA_ENABLED: "true"
  RUSTC_WRAPPER: "sccache"
  RUSTFLAGS: "-C debuginfo=0"
  CARGO_INCREMENTAL: 0

jobs:
  rustfmt:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly
          components: rustfmt
      - run: cargo fmt --manifest-path examples/diesel_sqlite/Cargo.toml --all -- --check
      - run: cargo fmt --manifest-path examples/diesel_postgres/Cargo.toml --all -- --check
      - run: cargo fmt --manifest-path examples/diesel_mysql/Cargo.toml --all -- --check

  diesel-build:
    name: Build `sea-query-diesel`
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Run sccache-cache
        uses: mozilla-actions/sccache-action@v0.0.9
      - run: cargo update --manifest-path sea-query-diesel/Cargo.toml -p ipnetwork@0.21.1 --precise=0.20.0
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-chrono,with-json,with-rust_decimal,with-bigdecimal,with-uuid,with-time,with-ipnetwork,with-mac_address,postgres-array,postgres-vector
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-chrono
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-json
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-rust_decimal
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres --features=with-rust_decimal-postgres
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features mysql --features=with-rust_decimal-mysql
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-bigdecimal
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-uuid
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-time
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-ipnetwork
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=with-mac_address
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=postgres-array
      - run: cargo build --manifest-path sea-query-diesel/Cargo.toml --workspace --features postgres,sqlite,mysql --features=postgres-vector

  sqlite:
    name: SQLite
    runs-on: ubuntu-latest
    needs: diesel-build
    strategy:
      matrix:
        example: [diesel_sqlite]
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Run sccache-cache
        uses: mozilla-actions/sccache-action@v0.0.9
      - run: cargo build --manifest-path examples/${{ matrix.example }}/Cargo.toml
      - run: cargo run --manifest-path examples/${{ matrix.example }}/Cargo.toml

  mysql:
    name: MySQL
    runs-on: ubuntu-latest
    needs: diesel-build
    strategy:
      matrix:
        version: [8.0, 5.7]
        example: [diesel_mysql]
    services:
      mysql:
        image: mysql:${{ matrix.version }}
        env:
          MYSQL_HOST: 127.0.0.1
          MYSQL_DATABASE: query
          MYSQL_USER: sea
          MYSQL_PASSWORD: sea
          MYSQL_ROOT_PASSWORD: sea
        ports:
          - "3306:3306"
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Run sccache-cache
        uses: mozilla-actions/sccache-action@v0.0.9
      - run: cargo build --manifest-path examples/${{ matrix.example }}/Cargo.toml
      - run: cargo run --manifest-path examples/${{ matrix.example }}/Cargo.toml

  mariadb:
    name: MariaDB
    runs-on: ubuntu-latest
    needs: diesel-build
    strategy:
      matrix:
        version: [10.6]
        example: [diesel_mysql]
    services:
      mariadb:
        image: mariadb:${{ matrix.version }}
        env:
          MYSQL_HOST: 127.0.0.1
          MYSQL_DATABASE: query
          MYSQL_USER: sea
          MYSQL_PASSWORD: sea
          MYSQL_ROOT_PASSWORD: sea
        ports:
          - "3306:3306"
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Run sccache-cache
        uses: mozilla-actions/sccache-action@v0.0.9
      - run: cargo build --manifest-path examples/${{ matrix.example }}/Cargo.toml
      - run: cargo run --manifest-path examples/${{ matrix.example }}/Cargo.toml

  postgres:
    name: PostgreSQL
    runs-on: ubuntu-latest
    needs: diesel-build
    strategy:
      matrix:
        version: [14.4, 13.7, 12.11]
        example: [diesel_postgres]
    services:
      postgres:
        image: postgres:${{ matrix.version }}
        env:
          POSTGRES_HOST: 127.0.0.1
          POSTGRES_DB: query
          POSTGRES_USER: sea
          POSTGRES_PASSWORD: sea
        ports:
          - "5432:5432"
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - name: Run sccache-cache
        uses: mozilla-actions/sccache-action@v0.0.9
      - run: cargo update --manifest-path examples/${{ matrix.example }}/Cargo.toml -p ipnetwork@0.21.1 --precise=0.20.0
      - run: cargo build --manifest-path examples/${{ matrix.example }}/Cargo.toml
      - run: cargo run --manifest-path examples/${{ matrix.example }}/Cargo.toml
